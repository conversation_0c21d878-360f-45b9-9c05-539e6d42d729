/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @alexander-akait
*/

"use strict";

/**
 * @type {ReadonlySet<never>}
 */
const NO_TYPES = new Set();

/**
 * @type {ReadonlySet<"asset">}
 */
const ASSET_TYPES = new Set(["asset"]);

/**
 * @type {ReadonlySet<"asset" | "javascript" | "asset">}
 */
const ASSET_AND_JS_TYPES = new Set(["asset", "javascript"]);

/**
 * @type {ReadonlySet<"css-url" | "asset">}
 */
const ASSET_AND_CSS_URL_TYPES = new Set(["asset", "css-url"]);

/**
 * @type {ReadonlySet<"javascript" | "css-url" | "asset">}
 */
const ASSET_AND_JS_AND_CSS_URL_TYPES = new Set([
	"asset",
	"javascript",
	"css-url"
]);

/**
 * @type {"javascript"}
 */
const JS_TYPE = "javascript";

/**
 * @type {ReadonlySet<"javascript">}
 */
const JS_TYPES = new Set(["javascript"]);

/**
 * @type {ReadonlySet<"javascript" | "css-export">}
 */
const JS_AND_CSS_EXPORT_TYPES = new Set(["javascript", "css-export"]);

/**
 * @type {ReadonlySet<"javascript" | "css-url">}
 */
const JS_AND_CSS_URL_TYPES = new Set(["javascript", "css-url"]);

/**
 * @type {ReadonlySet<"javascript" | "css">}
 */
const JS_AND_CSS_TYPES = new Set(["javascript", "css"]);

/**
 * @type {"css"}
 */
const CSS_TYPE = "css";
/**
 * @type {ReadonlySet<"css">}
 */
const CSS_TYPES = new Set(["css"]);

/**
 * @type {ReadonlySet<"css-url">}
 */
const CSS_URL_TYPES = new Set(["css-url"]);
/**
 * @type {ReadonlySet<"css-import">}
 */
const CSS_IMPORT_TYPES = new Set(["css-import"]);

/**
 * @type {ReadonlySet<"webassembly">}
 */
const WEBASSEMBLY_TYPES = new Set(["webassembly"]);

/**
 * @type {ReadonlySet<"runtime">}
 */
const RUNTIME_TYPES = new Set(["runtime"]);

/**
 * @type {ReadonlySet<"remote" | "share-init">}
 */
const REMOTE_AND_SHARE_INIT_TYPES = new Set(["remote", "share-init"]);

/**
 * @type {ReadonlySet<"consume-shared">}
 */
const CONSUME_SHARED_TYPES = new Set(["consume-shared"]);

/**
 * @type {ReadonlySet<"share-init">}
 */
const SHARED_INIT_TYPES = new Set(["share-init"]);

module.exports.ASSET_AND_CSS_URL_TYPES = ASSET_AND_CSS_URL_TYPES;
module.exports.ASSET_AND_JS_AND_CSS_URL_TYPES = ASSET_AND_JS_AND_CSS_URL_TYPES;
module.exports.ASSET_AND_JS_TYPES = ASSET_AND_JS_TYPES;
module.exports.ASSET_TYPES = ASSET_TYPES;
module.exports.CONSUME_SHARED_TYPES = CONSUME_SHARED_TYPES;
module.exports.CSS_IMPORT_TYPES = CSS_IMPORT_TYPES;
module.exports.CSS_TYPE = CSS_TYPE;
module.exports.CSS_TYPES = CSS_TYPES;
module.exports.CSS_URL_TYPES = CSS_URL_TYPES;
module.exports.JS_AND_CSS_EXPORT_TYPES = JS_AND_CSS_EXPORT_TYPES;
module.exports.JS_AND_CSS_TYPES = JS_AND_CSS_TYPES;
module.exports.JS_AND_CSS_URL_TYPES = JS_AND_CSS_URL_TYPES;
module.exports.JS_TYPE = JS_TYPE;
module.exports.JS_TYPES = JS_TYPES;
module.exports.NO_TYPES = NO_TYPES;
module.exports.REMOTE_AND_SHARE_INIT_TYPES = REMOTE_AND_SHARE_INIT_TYPES;
module.exports.RUNTIME_TYPES = RUNTIME_TYPES;
module.exports.SHARED_INIT_TYPES = SHARED_INIT_TYPES;
module.exports.WEBASSEMBLY_TYPES = WEBASSEMBLY_TYPES;
