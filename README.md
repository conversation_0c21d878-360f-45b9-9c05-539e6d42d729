# نظام محاسبة ويب حديث

هذا المشروع عبارة عن نظام محاسبة ويب حديث باستخدام Python (Flask) للباك اند و React للواجهة الأمامية.

## المجلدات:
- `backend`: كود الباك اند (Flask)
- `frontend`: كود الواجهة الأمامية (React)

## الميزات الأساسية:
- تسجيل الدخول
- إدارة العملاء
- إدارة الفواتير
- تقارير مالية
- واجهة مستخدم حديثة

## خطوات التشغيل:
1. إعداد وتشغيل الباك اند (Flask)
2. إعداد وتشغيل الواجهة الأمامية (React)

سيتم تحديث هذا الملف مع تقدم العمل.
