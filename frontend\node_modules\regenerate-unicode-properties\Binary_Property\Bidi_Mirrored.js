const set = require('regenerate')(0x3C, 0x3E, 0x5B, 0x5D, 0x7B, 0x7D, 0xAB, 0xBB, 0x2140, 0x2211, 0x2224, 0x2226, 0x2239, 0x2262, 0x2298, 0x27C0, 0x29B8, 0x29C9, 0x29E1, 0x2A24, 0x2A26, 0x2A29, 0x2ADC, 0x2ADE, 0x2AF3, 0x2AFD, 0x2BFE, 0xFF1C, 0xFF1E, 0xFF3B, 0xFF3D, 0xFF5B, 0xFF5D, 0x1D6DB, 0x1D715, 0x1D74F, 0x1D789, 0x1D7C3);
set.addRange(0x28, 0x29).addRange(0xF3A, 0xF3D).addRange(0x169B, 0x169C).addRange(0x2039, 0x203A).addRange(0x2045, 0x2046).addRange(0x207D, 0x207E).addRange(0x208D, 0x208E).addRange(0x2201, 0x2204).addRange(0x2208, 0x220D).addRange(0x2215, 0x2216).addRange(0x221A, 0x221D).addRange(0x221F, 0x2222).addRange(0x222B, 0x2233).addRange(0x223B, 0x224C).addRange(0x2252, 0x2255).addRange(0x225F, 0x2260).addRange(0x2264, 0x226B).addRange(0x226D, 0x228C).addRange(0x228F, 0x2292).addRange(0x22A2, 0x22A3).addRange(0x22A6, 0x22B8).addRange(0x22BE, 0x22BF).addRange(0x22C9, 0x22CD).addRange(0x22D0, 0x22D1).addRange(0x22D6, 0x22ED).addRange(0x22F0, 0x22FF).addRange(0x2308, 0x230B).addRange(0x2320, 0x2321).addRange(0x2329, 0x232A).addRange(0x2768, 0x2775).addRange(0x27C3, 0x27C6).addRange(0x27C8, 0x27C9).addRange(0x27CB, 0x27CD).addRange(0x27D3, 0x27D6).addRange(0x27DC, 0x27DE).addRange(0x27E2, 0x27EF).addRange(0x2983, 0x2998).addRange(0x299B, 0x29A0).addRange(0x29A2, 0x29AF).addRange(0x29C0, 0x29C5).addRange(0x29CE, 0x29D2).addRange(0x29D4, 0x29D5).addRange(0x29D8, 0x29DC).addRange(0x29E3, 0x29E5).addRange(0x29E8, 0x29E9).addRange(0x29F4, 0x29F9).addRange(0x29FC, 0x29FD).addRange(0x2A0A, 0x2A1C).addRange(0x2A1E, 0x2A21).addRange(0x2A2B, 0x2A2E).addRange(0x2A34, 0x2A35);
set.addRange(0x2A3C, 0x2A3E).addRange(0x2A57, 0x2A58).addRange(0x2A64, 0x2A65).addRange(0x2A6A, 0x2A6D).addRange(0x2A6F, 0x2A70).addRange(0x2A73, 0x2A74).addRange(0x2A79, 0x2AA3).addRange(0x2AA6, 0x2AAD).addRange(0x2AAF, 0x2AD6).addRange(0x2AE2, 0x2AE6).addRange(0x2AEC, 0x2AEE).addRange(0x2AF7, 0x2AFB).addRange(0x2E02, 0x2E05).addRange(0x2E09, 0x2E0A).addRange(0x2E0C, 0x2E0D).addRange(0x2E1C, 0x2E1D).addRange(0x2E20, 0x2E29).addRange(0x2E55, 0x2E5C).addRange(0x3008, 0x3011).addRange(0x3014, 0x301B).addRange(0xFE59, 0xFE5E).addRange(0xFE64, 0xFE65).addRange(0xFF08, 0xFF09).addRange(0xFF5F, 0xFF60).addRange(0xFF62, 0xFF63);
exports.characters = set;
