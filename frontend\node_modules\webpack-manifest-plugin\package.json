{"name": "webpack-manifest-plugin", "version": "4.1.1", "description": "A Webpack Plugin for generating Asset Manifests", "license": "MIT", "repository": "shellscape/webpack-manifest-plugin", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/shellscape/webpack-manifest-plugin", "bugs": "https://github.com/shellscape/webpack-manifest-plugin/issues", "main": "dist/index.js", "engines": {"node": ">=12.22.0"}, "scripts": {"build": "tsc --project tsconfig.json", "ci:coverage": "nyc pnpm ci:test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm lint && pnpm security", "ci:test": "pnpm test -- --verbose", "lint": "pnpm lint:docs && pnpm lint:json && pnpm lint:js", "lint-staged": "lint-staged", "lint:docs": "prettier --write README.md", "lint:js": "eslint --cache --fix --cache scripts src test", "lint:json": "prettier --write codecov.yml package.json", "posttest": "pnpm switch -- \"4\" && pnpm install", "prepublishOnly": "pnpm lint && pnpm build", "pretest": "pnpm build", "security": "pnpm audit --audit-level=high --prod", "switch": "cd scripts && ts-node --project ./tsconfig.json ./set-webpack-version.ts", "test": "pnpm test:v4 && pnpm test:v5", "test:v4": "ava", "test:v5": "pnpm switch -- \"5\" && pnpm install && ava"}, "files": ["dist", "README.md", "LICENSE"], "peerDependencies": {"webpack": "^4.44.2 || ^5.47.0"}, "dependencies": {"tapable": "^2.0.0", "webpack-sources": "^2.2.0"}, "devDependencies": {"@ava/babel": "^1.0.1", "@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^13.1.0", "@svgr/webpack": "^5.4.0", "@types/node": "^16.4.3", "@types/webpack": "^4.41.26", "@types/webpack-sources": "^2.1.1", "@wordpress/dependency-extraction-webpack-plugin": "^3.1.0", "ava": "^3.13.0", "codecov": "^3.1.0", "copy-webpack-plugin": "^6.2.1", "css-loader": "^1.0.0", "del": "^6.0.0", "eslint-config-shellscape": "^4.2.0", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "^2.0.0", "husky": "4.3.8", "lint-staged": "11.1.1", "memory-fs": "^0.4.1", "nyc": "^15.1.0", "pre-commit": "^1.2.2", "prettier": "^2.1.2", "prettier-plugin-package": "^1.2.0", "react": "^16.3.2", "style-loader": "^0.23.0", "ts-node": "^10.1.0", "tslib": "^2.3.0", "typescript": "^4.3.5", "webpack": "^4.44.2", "webpack-merge": "^5.2.0"}, "ava": {"files": ["!**/fixtures/**", "!**/helpers/**", "!**/output/**"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "jest": {"testEnvironment": "node", "coverageDirectory": "./coverage/", "collectCoverage": true}, "lint-staged": {"*.js": ["eslint --fix"]}, "maintainers": ["<PERSON> <<EMAIL>>"], "nyc": {"include": ["src/*.ts"], "exclude": ["test/"]}, "pre-commit": "lint-staged", "webpack-versions": {"4": {"webpack": "^4.44.2", "@types/webpack": "^4.41.26"}, "5": {"webpack": "latest", "@types/webpack": "latest"}}}