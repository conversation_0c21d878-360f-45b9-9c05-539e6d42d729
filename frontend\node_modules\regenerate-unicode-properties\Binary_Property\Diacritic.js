const set = require('regenerate')(0x5E, 0x60, 0xA8, 0xAF, 0xB4, 0x37A, 0x559, 0x5BF, 0x5C4, 0x93C, 0x94D, 0x971, 0x9BC, 0x9CD, 0xA3C, 0xA4D, 0xABC, 0xACD, 0xB3C, 0xB4D, 0xB55, 0xBCD, 0xC3C, 0xC4D, 0xCBC, 0xCCD, 0xD4D, 0xDCA, 0xE3A, 0xE4E, 0xEBA, 0xF35, 0xF37, 0xF39, 0xFC6, 0x1037, 0x108F, 0x1734, 0x17DD, 0x1A60, 0x1A7F, 0x1B34, 0x1B44, 0x1BE6, 0x1CED, 0x1CF4, 0x1FBD, 0x2E2F, 0x30FC, 0xA66F, 0xA67F, 0xA806, 0xA82C, 0xA8C4, 0xA953, 0xA9B3, 0xA9C0, 0xA9E5, 0xAAF6, 0xFB1E, 0xFF3E, 0xFF40, 0xFF70, 0xFFE3, 0x102E0, 0x10A3F, 0x10D4E, 0x11046, 0x11070, 0x11173, 0x111C0, 0x1134D, 0x11442, 0x11446, 0x1163F, 0x1172B, 0x11943, 0x119E0, 0x11A34, 0x11A47, 0x11A99, 0x11C3F, 0x11D42, 0x11D97, 0x11F5A, 0x1612F, 0x1E2AE);
set.addRange(0xB7, 0xB8).addRange(0x2B0, 0x34E).addRange(0x350, 0x357).addRange(0x35D, 0x362).addRange(0x374, 0x375).addRange(0x384, 0x385).addRange(0x483, 0x487).addRange(0x591, 0x5A1).addRange(0x5A3, 0x5BD).addRange(0x5C1, 0x5C2).addRange(0x64B, 0x652).addRange(0x657, 0x658).addRange(0x6DF, 0x6E0).addRange(0x6E5, 0x6E6).addRange(0x6EA, 0x6EC).addRange(0x730, 0x74A).addRange(0x7A6, 0x7B0).addRange(0x7EB, 0x7F5).addRange(0x818, 0x819).addRange(0x898, 0x89F).addRange(0x8C9, 0x8D2).addRange(0x8E3, 0x8FE).addRange(0x951, 0x954).addRange(0xAFD, 0xAFF).addRange(0xD3B, 0xD3C).addRange(0xE47, 0xE4C).addRange(0xEC8, 0xECC).addRange(0xF18, 0xF19).addRange(0xF3E, 0xF3F).addRange(0xF82, 0xF84).addRange(0xF86, 0xF87).addRange(0x1039, 0x103A).addRange(0x1063, 0x1064).addRange(0x1069, 0x106D).addRange(0x1087, 0x108D).addRange(0x109A, 0x109B).addRange(0x135D, 0x135F).addRange(0x1714, 0x1715).addRange(0x17C9, 0x17D3).addRange(0x1939, 0x193B).addRange(0x1A75, 0x1A7C).addRange(0x1AB0, 0x1ABE).addRange(0x1AC1, 0x1ACB).addRange(0x1B6B, 0x1B73).addRange(0x1BAA, 0x1BAB).addRange(0x1BF2, 0x1BF3).addRange(0x1C36, 0x1C37).addRange(0x1C78, 0x1C7D).addRange(0x1CD0, 0x1CE8).addRange(0x1CF7, 0x1CF9).addRange(0x1D2C, 0x1D6A);
set.addRange(0x1DC4, 0x1DCF).addRange(0x1DF5, 0x1DFF).addRange(0x1FBF, 0x1FC1).addRange(0x1FCD, 0x1FCF).addRange(0x1FDD, 0x1FDF).addRange(0x1FED, 0x1FEF).addRange(0x1FFD, 0x1FFE).addRange(0x2CEF, 0x2CF1).addRange(0x302A, 0x302F).addRange(0x3099, 0x309C).addRange(0xA67C, 0xA67D).addRange(0xA69C, 0xA69D).addRange(0xA6F0, 0xA6F1).addRange(0xA700, 0xA721).addRange(0xA788, 0xA78A).addRange(0xA7F8, 0xA7F9).addRange(0xA8E0, 0xA8F1).addRange(0xA92B, 0xA92E).addRange(0xAA7B, 0xAA7D).addRange(0xAABF, 0xAAC2).addRange(0xAB5B, 0xAB5F).addRange(0xAB69, 0xAB6B).addRange(0xABEC, 0xABED).addRange(0xFE20, 0xFE2F).addRange(0xFF9E, 0xFF9F).addRange(0x10780, 0x10785).addRange(0x10787, 0x107B0).addRange(0x107B2, 0x107BA).addRange(0x10A38, 0x10A3A).addRange(0x10AE5, 0x10AE6).addRange(0x10D22, 0x10D27).addRange(0x10D69, 0x10D6D).addRange(0x10EFD, 0x10EFF).addRange(0x10F46, 0x10F50).addRange(0x10F82, 0x10F85).addRange(0x110B9, 0x110BA).addRange(0x11133, 0x11134).addRange(0x111CA, 0x111CC).addRange(0x11235, 0x11236).addRange(0x112E9, 0x112EA).addRange(0x1133B, 0x1133C).addRange(0x11366, 0x1136C).addRange(0x11370, 0x11374).addRange(0x113CE, 0x113D0).addRange(0x113D2, 0x113D3).addRange(0x113E1, 0x113E2).addRange(0x114C2, 0x114C3).addRange(0x115BF, 0x115C0).addRange(0x116B6, 0x116B7).addRange(0x11839, 0x1183A).addRange(0x1193D, 0x1193E);
set.addRange(0x11D44, 0x11D45).addRange(0x11F41, 0x11F42).addRange(0x13447, 0x13455).addRange(0x16AF0, 0x16AF4).addRange(0x16B30, 0x16B36).addRange(0x16D6B, 0x16D6C).addRange(0x16F8F, 0x16F9F).addRange(0x16FF0, 0x16FF1).addRange(0x1AFF0, 0x1AFF3).addRange(0x1AFF5, 0x1AFFB).addRange(0x1AFFD, 0x1AFFE).addRange(0x1CF00, 0x1CF2D).addRange(0x1CF30, 0x1CF46).addRange(0x1D167, 0x1D169).addRange(0x1D16D, 0x1D172).addRange(0x1D17B, 0x1D182).addRange(0x1D185, 0x1D18B).addRange(0x1D1AA, 0x1D1AD).addRange(0x1E030, 0x1E06D).addRange(0x1E130, 0x1E136).addRange(0x1E2EC, 0x1E2EF).addRange(0x1E5EE, 0x1E5EF).addRange(0x1E8D0, 0x1E8D6).addRange(0x1E944, 0x1E946).addRange(0x1E948, 0x1E94A);
exports.characters = set;
